#!/usr/bin/env python3
import json
import os
import sys

def format_json_file(file_path):
    """格式化 JSON 文件"""
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析 JSON
        try:
            data = json.loads(content)
            # 格式化 JSON
            formatted_json = json.dumps(data, indent=4, ensure_ascii=False, sort_keys=True)
            
            # 创建备份
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已创建备份文件: {backup_path}")
            
            # 写入格式化后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_json)
            
            print(f"成功格式化文件: {file_path}")
            return True
            
        except json.JSONDecodeError as e:
            print(f"JSON 语法错误: {e}")
            print(f"错误位置: 行 {e.lineno}, 列 {e.colno}")
            
            # 尝试找到并显示错误行
            lines = content.split('\n')
            if e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                print(f"错误行内容: {error_line}")
                
                # 显示前后几行的上下文
                start = max(0, e.lineno - 3)
                end = min(len(lines), e.lineno + 2)
                print("\n上下文:")
                for i in range(start, end):
                    marker = " -> " if i == e.lineno - 1 else "    "
                    print(f"{marker}{i+1:3d}: {lines[i]}")
            
            return False
            
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return False
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

if __name__ == "__main__":
    settings_path = os.path.expanduser("~/Library/Application Support/Code/User/settings.json")
    print(f"正在处理文件: {settings_path}")
    print(f"文件是否存在: {os.path.exists(settings_path)}")
    if os.path.exists(settings_path):
        print(f"文件大小: {os.path.getsize(settings_path)} 字节")
    format_json_file(settings_path)
