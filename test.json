{
    "editor.fontSize": 18,
    "editor.multiCursorModifier": "ctrlCmd",
    "editor.suggestSelection": "first",
    "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
    "remote.SSH.remotePlatform": {
      "*************": "linux",
      "************": "linux",
      "************": "linux",
      "***********": "linux",
      "D30Station": "linux",
      "GPUServer": "linux",
      "Ali39.100.102.4": "linux",
      "D30Station_Out": "linux",
      "AliServer": "linux",
      "R740XD-panxiong": "linux",
      "AutoDL": "linux",
      "LocalHost": "windows"
    },
    "workbench.editorAssociations": {
        "*.ipynb": "jupyter-notebook"
    },
    "notebook.cellToolbarLocation": {
        "default": "right",
        "jupyter-notebook": "left"
    },
    "security.workspace.trust.untrustedFiles": "open",
    "files.autoGuessEncoding": true,
    "code-runner.runInTerminal": true,
    "code-runner.saveAllFilesBeforeRun": true,
    "files.associations": {
        "*.bat": "gmt"
    },
    "code-runner.saveFileBeforeRun": true,
    "[gmt]": {},
    "code-runner.executorMapByFileExtension": {
        ".bat": "batch",
        ".vb": "cd $dir && vbc /nologo $fileName && $dir$fileNameWithoutExt",
        ".vbs": "cscript //Nologo",
        ".scala": "scala",
        ".jl": "julia",
        ".cr": "crystal",
        ".ml": "ocaml",
        ".exs": "elixir",
        ".hx": "haxe --cwd $dirWithoutTrailingSlash --run $fileNameWithoutExt",
        ".rkt": "racket",
        ".scm": "csi -script",
        ".ahk": "autohotkey",
        ".au3": "autoit3",
        ".kt": "cd $dir && kotlinc $fileName -include-runtime -d $fileNameWithoutExt.jar && java -jar $fileNameWithoutExt.jar",
        ".kts": "kotlinc -script",
        ".dart": "dart",
        ".pas": "cd $dir && fpc $fileName && $dir$fileNameWithoutExt",
        ".pp": "cd $dir && fpc $fileName && $dir$fileNameWithoutExt",
        ".d": "cd $dir && dmd $fileName && $dir$fileNameWithoutExt",
        ".hs": "runhaskell",
        ".nim": "nim compile --verbosity:0 --hints:off --run",
        ".csproj": "dotnet run --project",
        ".fsproj": "dotnet run --project",
        ".lisp": "sbcl --script",
        ".kit": "kitc --run",
        ".v": "v run",
        ".vsh": "v run",
        ".sass": "sass --style expanded",
        ".cu": "cd $dir && nvcc $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
        ".ring": "ring"
    },
    "editor.accessibilityPageSize": 14,
    "oracledevtools.bookmarkFileFolder": "C:\\Users\\<USER>\\Oracle\\oracle.oracledevtools",
    "oracledevtools.download.otherFolder": "C:\\Users\\<USER>\\downloads",
    "oracledevtools.connectionConfiguration.configFilesFolder": "C:\\Users\\<USER>\\Oracle\\network\\admin",
    "oracledevtools.connectionConfiguration.walletFileFolder": "C:\\Users\\<USER>\\Oracle\\network\\admin",
    "files.autoSave": "afterDelay",
    "editor.unicodeHighlight.nonBasicASCII": false,
    "python.defaultInterpreterPath": "C:\\ProgramData\\miniconda3\\python.exe",
    "editor.inlineSuggest.enabled": true,
    "workbench.iconTheme": "material-icon-theme",
    "terminal.integrated.tabs.location": "left",
    "editor.minimap.enabled": false,
    "workbench.editor.empty.hint": "hidden",
    "security.allowedUNCHosts": [
        "wsl.localhost"
    ],
    "github.copilot.advanced": {},
    "editor.unicodeHighlight.ambiguousCharacters": false,
    "editor.formatOnSave": true,
    "[python]": {
    
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.formatOnSave": true
    },
    "editor.formatOnPaste": true,
    "editor.stickyScroll.enabled": true,
    "workbench.tree.enableStickyScroll": true,
    "editor.defaultFormatter": "ms-python.black-formatter",
    "python.formatting.provider": "autopep8", 
    "python.formatting.autopep8Args": [
        "--max-line-length",
        "120"
    ],
    "black-formatter.args": [
        "--line-length=150"
    ],
    "terminal.integrated.inheritEnv": false,
    "editor.unicodeHighlight.invisibleCharacters": false,
    "remote.autoForwardPortsSource": "hybrid",
    "jupyter.askForKernelRestart": false,
    "oracledevtools.connections": [
        {
            "connectionString": "Data Source=**********/pdbdd;user ID=qzguest;password=********;",
            "connectionType": 4,
            "tnsAdmin": "C:\\Users\\<USER>\\Oracle\\network\\admin",
            "name": "Connection.**********",
            "color": "none",
            "currentSchema": "",
            "addSettingsScopeToConnectionName": false,
            "addCurrentSchemaToConnectionName": false,
            "filters": []
        },
        {
            "connectionString": "Data Source=10.5.211.141/pdbqz;user ID=IES;password=************;",
            "connectionType": 4,
            "tnsAdmin": "C:\\Users\\<USER>\\Oracle\\network\\admin",
            "name": "Connection.10.5.211.141",
            "color": "none",
            "currentSchema": "",
            "addSettingsScopeToConnectionName": false,
            "addCurrentSchemaToConnectionName": false,
            "filters": []
        },
        {
            "connectionString": "Data Source=10.5.109.109/pdbqz;user ID=qzguest;password=********;",
            "connectionType": 4,
            "tnsAdmin": "C:\\Users\\<USER>\\Oracle\\network\\admin",
            "name": "Connection.1",
            "color": "none",
            "currentSchema": "",
            "addSettingsScopeToConnectionName": false,
            "addCurrentSchemaToConnectionName": false,
            "filters": []
        }
    ],
    "markdown-preview-enhanced.previewTheme": "one-light.css",
    "markdown-preview-enhanced.alwaysShowBacklinksInPreview": true,
    "markdown-preview-enhanced.enablePreviewZenMode": false,
    "cmake.options.statusBarVisibility": "visible",
    "markdown-preview-enhanced.usePandocParser": true,
    "workbench.settings.applyToAllProfiles": [
        "markdown-preview-enhanced.pandocPath"
    ],
    "markdown-preview-enhanced.latexEngine": "xelatex",
    "github.copilot.enable": {
        "*": false
    },
    "augment.chat.userGuidelines": "Always respond in Chinese-simplified\n不要写 markdown 文档\n不需要编写测试\n不需要运行程序",
    "terminal.integrated.env.linux": {
        "PATH": "/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/miniconda3/envs/pytorch-keras/bin:/home/<USER>/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    },
    "sqldeveloper.sqlHistory.historyLimit": 500,
    "sqldeveloper.telemetry.enabled": true,
    "gitlens.ai.model": "gitkraken",
    "gitlens.ai.gitkraken.model": "gemini:gemini-2.0-flash",
    "latex-workshop.latex.tools": [
     {
         "name": "xelatex",
         "command": "xelatex",
         "args": [
           "-synctex=1",
           "-interaction=nonstopmode",
           "-file-line-error",
           "%DOC%"
         ]
     },
     {
         "name": "pdflatex",
         "command": "pdflatex",
         "args": [
           "-synctex=1",
           "-interaction=nonstopmode",
           "-file-line-error",
           "%DOC%"
         ]
     },
     {
         "name": "bibtex",
         "command": "bibtex",
         "args": [
           "%DOCFILE%"
         ]
     }
 ],

 "latex-workshop.latex.recipes": [
   {
     "name": "pdflatex -> bibtex -> pdflatex*2",
     "tools": [
       "pdflatex",
       "bibtex",
       "pdflatex",
       "pdflatex"
     ]
   },
     {
       "name": "XeLaTeX",
       "tools": [
         "xelatex"
       ]
     },
     {
       "name": "PDFLaTeX",
       "tools": [
         "pdflatex"
       ]
     }, 
     {
       "name": "latexmk",
       "tools": [
         "latexmk"
       ]
     },
     {
       "name": "BibTeX",
       "tools": [
         "bibtex"
       ]
     },
     {
       "name": "xelatex -> bibtex -> xelatex*2",
       "tools": [
         "xelatex",
         "bibtex",
         "xelatex",
         "xelatex"
       ]
     }
 ],
 "latex-workshop.formatting.latex": "latexindent",
    "latex-workshop.view.pdf.ref.viewer": "tabOrBrowser",
        // 是否启用 IntelliSense，自动补全引用的包中的环境和命令
    "latex-workshop.intellisense.package.enabled": true,
    // 默认编译引擎为上次使用的
    "latex-workshop.latex.recipe.default": "lastUsed",
    // 预览复杂公式，使用时需要通过 command palette (命令面板) 打开
    "latex-workshop.mathpreviewpanel.cursor.enabled": true,
    // 不允许弹窗显示错误信息
    "latex-workshop.message.error.show": false,
    // 不允许弹窗显示警告信息
    "latex-workshop.message.warning.show": false,
    // 预览 PDF 时，自动检测是否需要反转颜色
    "latex-workshop.view.pdf.invertMode.enabled": "auto",  
    "latex-workshop.latex.autoBuild.run": "onSave",
    "latex-workshop.latex.autoClean.run": "onBuilt",
    
    "[latex]": {
    "editor.wordWrap": "on"
    },
    "markdown-preview-enhanced.pandocArguments": [
        "--filter=pandoc-crossref",
        "--number-sections"
    ],
    "roo-cline.allowedCommands": [
        "npm test",
        "npm install",
        "tsc",
        "git log",
        "git diff",
        "git show"
    ],
 "git.autofetch": false,
 "git.postCommitCommand": "none",
  "git.enableSmartCommit": false,
  "terminal.integrated.commandsToSkipShell": [
    "matlab.interrupt"
  ],
  "github.copilot.chat.agent.thinkingTool": true,
  "cSpell.enabled": false
}